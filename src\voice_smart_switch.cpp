#include "voice_hybrid_system.h"
#include <esp_log.h>
#include <LittleFS.h>
#include <vector>
#include <algorithm>

static const char *TAG = "VoiceSmart";

// 外部声明
extern String audio_path;
extern voice_system_config_t system_config;

// 内部结构定义
typedef struct
{
    voice_source_type_t source;
    uint32_t access_time;
    uint32_t file_size;
    uint16_t version;
    float quality_score;
    float reliability_score;
    float speed_score;
} voice_source_candidate_t;

// 内部函数声明
static voice_error_t evaluate_voice_sources(const char *voice_name,
                                            std::vector<voice_source_candidate_t> &candidates);
static float calculate_quality_score(const voice_source_candidate_t &candidate);
static float calculate_reliability_score(const voice_source_candidate_t &candidate);
static float calculate_speed_score(const voice_source_candidate_t &candidate);
static voice_error_t select_best_source(const std::vector<voice_source_candidate_t> &candidates,
                                        voice_priority_policy_t policy,
                                        voice_file_info_t *selected_info);
static voice_error_t fallback_to_next_source(const char *voice_name,
                                             voice_source_type_t failed_source,
                                             voice_file_info_t *fallback_info);
static bool is_source_available(voice_source_type_t source, const char *voice_name);
static voice_error_t get_source_info(voice_source_type_t source, const char *voice_name,
                                     voice_source_candidate_t *candidate);

/**
 * @brief 获取语音文件信息（智能选择）
 */
voice_error_t voice_get_file_info(const char *voice_name, voice_file_info_t *info)
{
    if (!voice_name || !info)
    {
        return VOICE_ERR_INVALID_PARAM;
    }

    ESP_LOGD(TAG, "Getting smart file info for: %s", voice_name);

    // 评估所有可用的语音源
    std::vector<voice_source_candidate_t> candidates;
    voice_error_t err = evaluate_voice_sources(voice_name, candidates);
    if (err != VOICE_ERR_OK)
    {
        ESP_LOGE(TAG, "Failed to evaluate voice sources: %d", err);
        return err;
    }

    if (candidates.empty())
    {
        ESP_LOGW(TAG, "No voice sources available for: %s", voice_name);
        return VOICE_ERR_FILE_NOT_FOUND;
    }

    // 根据策略选择最佳源
    err = select_best_source(candidates, system_config.priority_policy, info);
    if (err != VOICE_ERR_OK)
    {
        ESP_LOGE(TAG, "Failed to select best source: %d", err);
        return err;
    }

    ESP_LOGI(TAG, "Selected source %d for %s (quality=%.2f, reliability=%.2f, speed=%.2f)",
             info->source, voice_name,
             calculate_quality_score(&candidates[0]),
             calculate_reliability_score(&candidates[0]),
             calculate_speed_score(&candidates[0]));

    return VOICE_ERR_OK;
}

/**
 * @brief 评估所有可用的语音源
 */
static voice_error_t evaluate_voice_sources(const char *voice_name,
                                            std::vector<voice_source_candidate_t> &candidates)
{
    candidates.clear();

    // 检查所有可能的源
    voice_source_type_t sources[] = {
        VOICE_SOURCE_RAM_CACHE,
        VOICE_SOURCE_LITTLEFS,
        VOICE_SOURCE_FLASH_ROM,
        VOICE_SOURCE_DEFAULT_TONE};

    for (int i = 0; i < 4; i++)
    {
        if (is_source_available(sources[i], voice_name))
        {
            voice_source_candidate_t candidate;
            voice_error_t err = get_source_info(sources[i], voice_name, &candidate);
            if (err == VOICE_ERR_OK)
            {
                // 计算评分
                candidate.quality_score = calculate_quality_score(&candidate);
                candidate.reliability_score = calculate_reliability_score(&candidate);
                candidate.speed_score = calculate_speed_score(&candidate);

                candidates.push_back(candidate);
                ESP_LOGD(TAG, "Source %d: quality=%.2f, reliability=%.2f, speed=%.2f",
                         candidate.source, candidate.quality_score,
                         candidate.reliability_score, candidate.speed_score);
            }
        }
    }

    return VOICE_ERR_OK;
}

/**
 * @brief 计算质量评分
 */
static float calculate_quality_score(const voice_source_candidate_t *candidate)
{
    if (!candidate)
        return 0.0f;

    float score = 0.0f;

    switch (candidate->source)
    {
    case VOICE_SOURCE_LITTLEFS:
        score = 1.0f; // 云端下载的文件质量最高
        break;
    case VOICE_SOURCE_FLASH_ROM:
        score = 0.8f; // ROM中的文件质量较高
        break;
    case VOICE_SOURCE_RAM_CACHE:
        score = 0.9f; // 缓存文件质量高
        break;
    case VOICE_SOURCE_DEFAULT_TONE:
        score = 0.3f; // 默认提示音质量较低
        break;
    default:
        score = 0.0f;
        break;
    }

    // 根据文件大小调整评分（较大的文件通常质量更好）
    if (candidate->file_size > 50000)
    {
        score += 0.1f;
    }
    else if (candidate->file_size < 10000)
    {
        score -= 0.1f;
    }

    // 根据版本调整评分
    if (candidate->version > 1)
    {
        score += 0.05f * (candidate->version - 1);
    }

    return std::min(1.0f, std::max(0.0f, score));
}

/**
 * @brief 计算可靠性评分
 */
static float calculate_reliability_score(const voice_source_candidate_t *candidate)
{
    if (!candidate)
        return 0.0f;

    float score = 0.0f;

    switch (candidate->source)
    {
    case VOICE_SOURCE_FLASH_ROM:
        score = 1.0f; // ROM最可靠
        break;
    case VOICE_SOURCE_LITTLEFS:
        score = 0.8f; // LittleFS较可靠
        break;
    case VOICE_SOURCE_RAM_CACHE:
        score = 0.6f; // RAM缓存可能丢失
        break;
    case VOICE_SOURCE_DEFAULT_TONE:
        score = 0.9f; // 默认提示音很可靠
        break;
    default:
        score = 0.0f;
        break;
    }

    return score;
}

/**
 * @brief 计算速度评分
 */
static float calculate_speed_score(const voice_source_candidate_t *candidate)
{
    if (!candidate)
        return 0.0f;

    float score = 0.0f;

    switch (candidate->source)
    {
    case VOICE_SOURCE_RAM_CACHE:
        score = 1.0f; // RAM访问最快
        break;
    case VOICE_SOURCE_LITTLEFS:
        score = 0.7f; // LittleFS访问较快
        break;
    case VOICE_SOURCE_FLASH_ROM:
        score = 0.5f; // ROM访问需要解压，较慢
        break;
    case VOICE_SOURCE_DEFAULT_TONE:
        score = 0.8f; // 默认提示音较快
        break;
    default:
        score = 0.0f;
        break;
    }

    // 根据文件大小调整评分（较小的文件加载更快）
    if (candidate->file_size < 10000)
    {
        score += 0.1f;
    }
    else if (candidate->file_size > 100000)
    {
        score -= 0.1f;
    }

    return std::min(1.0f, std::max(0.0f, score));
}

/**
 * @brief 选择最佳源
 */
static voice_error_t select_best_source(const std::vector<voice_source_candidate_t> &candidates,
                                        voice_priority_policy_t policy,
                                        voice_file_info_t *selected_info)
{
    if (candidates.empty() || !selected_info)
    {
        return VOICE_ERR_INVALID_PARAM;
    }

    // 计算综合评分
    std::vector<std::pair<float, int>> scores;

    for (size_t i = 0; i < candidates.size(); i++)
    {
        const auto &candidate = candidates[i];
        float total_score = 0.0f;

        switch (policy)
        {
        case VOICE_PRIORITY_SPEED_FIRST:
            total_score = candidate.speed_score * 0.6f +
                          candidate.quality_score * 0.3f +
                          candidate.reliability_score * 0.1f;
            break;

        case VOICE_PRIORITY_QUALITY_FIRST:
            total_score = candidate.quality_score * 0.6f +
                          candidate.reliability_score * 0.3f +
                          candidate.speed_score * 0.1f;
            break;

        case VOICE_PRIORITY_RELIABILITY_FIRST:
            total_score = candidate.reliability_score * 0.6f +
                          candidate.quality_score * 0.2f +
                          candidate.speed_score * 0.2f;
            break;

        default:
            total_score = (candidate.quality_score + candidate.reliability_score + candidate.speed_score) / 3.0f;
            break;
        }

        scores.push_back(std::make_pair(total_score, i));
    }

    // 按评分排序
    std::sort(scores.begin(), scores.end(),
              [](const std::pair<float, int> &a, const std::pair<float, int> &b)
              {
                  return a.first > b.first;
              });

    // 选择最高评分的源
    const auto &best_candidate = candidates[scores[0].second];

    memset(selected_info, 0, sizeof(voice_file_info_t));
    selected_info->source = best_candidate.source;
    selected_info->file_size = best_candidate.file_size;
    selected_info->version = best_candidate.version;

    // 设置文件路径
    switch (best_candidate.source)
    {
    case VOICE_SOURCE_LITTLEFS:
        snprintf(selected_info->file_path, MAX_FILEPATH_LEN, "%s/%s.wav",
                 audio_path.c_str(), "voice_name"); // 需要传入实际文件名
        break;
    case VOICE_SOURCE_FLASH_ROM:
        strncpy(selected_info->file_path, "voice_name", MAX_FILEPATH_LEN - 1);
        selected_info->is_compressed = true;
        break;
    case VOICE_SOURCE_DEFAULT_TONE:
        strncpy(selected_info->file_path, "default_tone", MAX_FILEPATH_LEN - 1);
        break;
    default:
        break;
    }

    ESP_LOGD(TAG, "Selected source %d with score %.3f",
             best_candidate.source, scores[0].first);

    return VOICE_ERR_OK;
}

/**
 * @brief 检查源是否可用
 */
static bool is_source_available(voice_source_type_t source, const char *voice_name)
{
    if (!voice_name)
        return false;

    switch (source)
    {
    case VOICE_SOURCE_RAM_CACHE:
        // 简化：这里应该检查RAM缓存
        return false; // 暂时不支持RAM缓存检查

    case VOICE_SOURCE_LITTLEFS:
    {
        String file_path = audio_path + "/" + String(voice_name) + ".wav";
        return LittleFS.exists(file_path);
    }

    case VOICE_SOURCE_FLASH_ROM:
        return voice_rom_file_exists(voice_name);

    case VOICE_SOURCE_DEFAULT_TONE:
        return true; // 默认提示音总是可用

    default:
        return false;
    }
}

/**
 * @brief 获取源信息
 */
static voice_error_t get_source_info(voice_source_type_t source, const char *voice_name,
                                     voice_source_candidate_t *candidate)
{
    if (!voice_name || !candidate)
    {
        return VOICE_ERR_INVALID_PARAM;
    }

    memset(candidate, 0, sizeof(voice_source_candidate_t));
    candidate->source = source;
    candidate->access_time = millis();

    switch (source)
    {
    case VOICE_SOURCE_LITTLEFS:
    {
        String file_path = audio_path + "/" + String(voice_name) + ".wav";
        File file = LittleFS.open(file_path, "r");
        if (file)
        {
            candidate->file_size = file.size();
            file.close();

            // 获取版本信息
            uint16_t version = 0;
            String version_file = audio_path + "/." + String(voice_name) + ".version";
            File ver_file = LittleFS.open(version_file, "r");
            if (ver_file)
            {
                version = ver_file.readString().toInt();
                ver_file.close();
            }
            candidate->version = version;
        }
        else
        {
            return VOICE_ERR_FILE_NOT_FOUND;
        }
    }
    break;

    case VOICE_SOURCE_FLASH_ROM:
    {
        voice_file_entry_t entry;
        voice_error_t err = voice_rom_get_file_entry(voice_name, &entry);
        if (err == VOICE_ERR_OK)
        {
            candidate->file_size = entry.original_size;
            candidate->version = entry.version;
        }
        else
        {
            return err;
        }
    }
    break;

    case VOICE_SOURCE_DEFAULT_TONE:
        candidate->file_size = 1000; // 假设默认提示音大小
        candidate->version = 1;
        break;

    case VOICE_SOURCE_RAM_CACHE:
        // 简化：这里应该从缓存获取信息
        return VOICE_ERR_FILE_NOT_FOUND;

    default:
        return VOICE_ERR_INVALID_PARAM;
    }

    return VOICE_ERR_OK;
}

/**
 * @brief 回退到下一个可用源
 */
static voice_error_t fallback_to_next_source(const char *voice_name,
                                             voice_source_type_t failed_source,
                                             voice_file_info_t *fallback_info)
{
    if (!voice_name || !fallback_info)
    {
        return VOICE_ERR_INVALID_PARAM;
    }

    ESP_LOGW(TAG, "Source %d failed, trying fallback for %s", failed_source, voice_name);

    // 定义回退顺序
    std::vector<voice_source_type_t> fallback_order;

    switch (failed_source)
    {
    case VOICE_SOURCE_RAM_CACHE:
        fallback_order = {VOICE_SOURCE_LITTLEFS, VOICE_SOURCE_FLASH_ROM, VOICE_SOURCE_DEFAULT_TONE};
        break;
    case VOICE_SOURCE_LITTLEFS:
        fallback_order = {VOICE_SOURCE_FLASH_ROM, VOICE_SOURCE_DEFAULT_TONE};
        break;
    case VOICE_SOURCE_FLASH_ROM:
        fallback_order = {VOICE_SOURCE_LITTLEFS, VOICE_SOURCE_DEFAULT_TONE};
        break;
    default:
        fallback_order = {VOICE_SOURCE_DEFAULT_TONE};
        break;
    }

    // 尝试每个回退源
    for (voice_source_type_t source : fallback_order)
    {
        if (is_source_available(source, voice_name))
        {
            voice_source_candidate_t candidate;
            voice_error_t err = get_source_info(source, voice_name, &candidate);
            if (err == VOICE_ERR_OK)
            {
                // 填充回退信息
                memset(fallback_info, 0, sizeof(voice_file_info_t));
                fallback_info->source = source;
                fallback_info->file_size = candidate.file_size;
                fallback_info->version = candidate.version;

                switch (source)
                {
                case VOICE_SOURCE_LITTLEFS:
                    snprintf(fallback_info->file_path, MAX_FILEPATH_LEN, "%s/%s.wav",
                             audio_path.c_str(), voice_name);
                    break;
                case VOICE_SOURCE_FLASH_ROM:
                    strncpy(fallback_info->file_path, voice_name, MAX_FILEPATH_LEN - 1);
                    fallback_info->is_compressed = true;
                    break;
                case VOICE_SOURCE_DEFAULT_TONE:
                    strncpy(fallback_info->file_path, "default_tone", MAX_FILEPATH_LEN - 1);
                    break;
                default:
                    break;
                }

                ESP_LOGI(TAG, "Fallback to source %d for %s", source, voice_name);
                return VOICE_ERR_OK;
            }
        }
    }

    ESP_LOGE(TAG, "No fallback source available for %s", voice_name);
    return VOICE_ERR_FILE_NOT_FOUND;
}

/**
 * @brief 智能播放语音（带自动回退）
 */
voice_error_t voice_play_smart(const char *voice_name)
{
    if (!voice_name)
    {
        return VOICE_ERR_INVALID_PARAM;
    }

    ESP_LOGI(TAG, "Smart playing voice: %s", voice_name);

    // 获取最佳语音源
    voice_file_info_t file_info;
    voice_error_t err = voice_get_file_info(voice_name, &file_info);
    if (err != VOICE_ERR_OK)
    {
        ESP_LOGE(TAG, "Failed to get file info for %s: %d", voice_name, err);
        return err;
    }

    // 尝试播放
    err = voice_play(voice_name);
    if (err == VOICE_ERR_OK)
    {
        return VOICE_ERR_OK;
    }

    // 播放失败，尝试回退
    ESP_LOGW(TAG, "Primary source failed, trying fallback...");

    voice_file_info_t fallback_info;
    err = fallback_to_next_source(voice_name, file_info.source, &fallback_info);
    if (err != VOICE_ERR_OK)
    {
        ESP_LOGE(TAG, "No fallback available for %s", voice_name);
        return err;
    }

    // 使用回退源播放
    return voice_play(voice_name);
}

#include "voice_hybrid_system.h"
#include <FS.h>
#include <LittleFS.h>
#include <HTTPClient.h>
#include <WiFiClientSecure.h>
#include <ArduinoJson.h>
#include <esp_log.h>

static const char *TAG = "VoiceCloud";

// 外部声明
extern String wav_file_path_base;
extern String wav_file_path[10];
extern uint8_t wav_exist_flag[10];
extern String audio_path;
extern const char *wav_client_certificate;

// 内部结构定义
typedef struct
{
    String filename;
    uint16_t version;
    uint32_t size;
    String checksum;
    String download_url;
} cloud_voice_info_t;

typedef struct
{
    String filename;
    uint16_t local_version;
    uint16_t cloud_version;
    bool needs_update;
} version_check_result_t;

// 内部函数声明
static voice_error_t check_cloud_version_info(std::vector<cloud_voice_info_t> &cloud_files);
static voice_error_t download_voice_file_with_retry(const cloud_voice_info_t &file_info);
static voice_error_t verify_downloaded_file(const String &filepath, const String &expected_checksum);
static voice_error_t get_local_file_version(const String &filename, uint16_t *version);
static voice_error_t save_local_file_version(const String &filename, uint16_t version);
static String calculate_file_checksum(const String &filepath);
static voice_error_t cleanup_old_files(void);

/**
 * @brief 检查文件版本
 */
voice_version_compare_result_t voice_check_version(const char *voice_name)
{
    if (!voice_name)
    {
        return VERSION_INVALID;
    }

    ESP_LOGI(TAG, "Checking version for: %s", voice_name);

    // 获取本地版本
    uint16_t local_version = 0;
    voice_error_t err = get_local_file_version(String(voice_name), &local_version);
    if (err != VOICE_ERR_OK)
    {
        ESP_LOGW(TAG, "Failed to get local version for %s", voice_name);
        local_version = 0;
    }

    // 检查ROM版本
    uint16_t rom_version = 0;
    voice_file_entry_t rom_entry;
    if (voice_rom_get_file_entry(voice_name, &rom_entry) == VOICE_ERR_OK)
    {
        rom_version = rom_entry.version;
    }

    // 获取云端版本信息
    std::vector<cloud_voice_info_t> cloud_files;
    err = check_cloud_version_info(cloud_files);
    if (err != VOICE_ERR_OK)
    {
        ESP_LOGW(TAG, "Failed to check cloud version info");
        // 比较本地和ROM版本
        if (local_version > rom_version)
        {
            return VERSION_CLOUD_NEWER; // 本地文件较新
        }
        else if (rom_version > local_version)
        {
            return VERSION_ROM_NEWER;
        }
        else
        {
            return VERSION_SAME;
        }
    }

    // 查找云端文件信息
    uint16_t cloud_version = 0;
    for (const auto &file : cloud_files)
    {
        if (file.filename == String(voice_name))
        {
            cloud_version = file.version;
            break;
        }
    }

    // 比较版本
    uint16_t max_local = std::max(local_version, rom_version);
    if (cloud_version > max_local)
    {
        return VERSION_CLOUD_NEWER;
    }
    else if (max_local > cloud_version)
    {
        return VERSION_ROM_NEWER;
    }
    else
    {
        return VERSION_SAME;
    }
}

/**
 * @brief 从云端更新语音文件
 */
voice_error_t voice_update_from_cloud(const char *voice_name)
{
    if (!voice_name)
    {
        return VOICE_ERR_INVALID_PARAM;
    }

    ESP_LOGI(TAG, "Updating voice file from cloud: %s", voice_name);

    // 获取云端文件信息
    std::vector<cloud_voice_info_t> cloud_files;
    voice_error_t err = check_cloud_version_info(cloud_files);
    if (err != VOICE_ERR_OK)
    {
        ESP_LOGE(TAG, "Failed to get cloud file info");
        return err;
    }

    // 查找目标文件
    cloud_voice_info_t target_file;
    bool found = false;
    for (const auto &file : cloud_files)
    {
        if (file.filename == String(voice_name))
        {
            target_file = file;
            found = true;
            break;
        }
    }

    if (!found)
    {
        ESP_LOGE(TAG, "File not found in cloud: %s", voice_name);
        return VOICE_ERR_FILE_NOT_FOUND;
    }

    // 检查本地版本
    uint16_t local_version = 0;
    get_local_file_version(target_file.filename, &local_version);

    if (target_file.version <= local_version)
    {
        ESP_LOGI(TAG, "Local file is up to date: %s (v%d)", voice_name, local_version);
        return VOICE_ERR_OK;
    }

    // 下载文件
    err = download_voice_file_with_retry(target_file);
    if (err != VOICE_ERR_OK)
    {
        ESP_LOGE(TAG, "Failed to download file: %s", voice_name);
        return err;
    }

    // 保存版本信息
    save_local_file_version(target_file.filename, target_file.version);

    ESP_LOGI(TAG, "Successfully updated voice file: %s (v%d)", voice_name, target_file.version);
    return VOICE_ERR_OK;
}

/**
 * @brief 检查云端版本信息
 */
static voice_error_t check_cloud_version_info(std::vector<cloud_voice_info_t> &cloud_files)
{
    HTTPClient https;
    WiFiClientSecure *client = new WiFiClientSecure;

    if (!client)
    {
        return VOICE_ERR_INSUFFICIENT_MEMORY;
    }

    client->setCACert(wav_client_certificate);

    // 构建版本检查URL
    String version_url = wav_file_path_base + "version.json";

    https.begin(*client, version_url);
    https.setTimeout(10000); // 10秒超时

    int httpCode = https.GET();

    if (httpCode != HTTP_CODE_OK)
    {
        ESP_LOGE(TAG, "HTTP request failed: %d", httpCode);
        https.end();
        delete client;
        return VOICE_ERR_NETWORK_TIMEOUT;
    }

    String payload = https.getString();
    https.end();
    delete client;

    // 解析JSON响应
    DynamicJsonDocument doc(2048);
    DeserializationError error = deserializeJson(doc, payload);

    if (error)
    {
        ESP_LOGE(TAG, "JSON parsing failed: %s", error.c_str());
        return VOICE_ERR_INVALID_PARAM;
    }

    // 提取文件信息
    cloud_files.clear();
    JsonArray files = doc["files"];

    for (JsonObject file : files)
    {
        cloud_voice_info_t info;
        info.filename = file["name"].as<String>();
        info.version = file["version"].as<uint16_t>();
        info.size = file["size"].as<uint32_t>();
        info.checksum = file["checksum"].as<String>();
        info.download_url = file["url"].as<String>();

        cloud_files.push_back(info);
    }

    ESP_LOGI(TAG, "Retrieved info for %d cloud files", cloud_files.size());
    return VOICE_ERR_OK;
}

/**
 * @brief 带重试的文件下载
 */
static voice_error_t download_voice_file_with_retry(const cloud_voice_info_t &file_info)
{
    const int MAX_RETRIES = 3;
    const int RETRY_DELAY_MS = 2000;

    String local_path = audio_path + "/" + file_info.filename + ".wav";
    String temp_path = audio_path + "/temp_" + file_info.filename + ".wav";

    for (int retry = 0; retry < MAX_RETRIES; retry++)
    {
        ESP_LOGI(TAG, "Downloading %s (attempt %d/%d)",
                 file_info.filename.c_str(), retry + 1, MAX_RETRIES);

        HTTPClient https;
        WiFiClientSecure *client = new WiFiClientSecure;

        if (!client)
        {
            return VOICE_ERR_INSUFFICIENT_MEMORY;
        }

        client->setCACert(wav_client_certificate);

        // 使用自定义URL或默认URL
        String download_url = file_info.download_url.isEmpty() ? (wav_file_path_base + file_info.filename + ".wav") : file_info.download_url;

        https.begin(*client, download_url);
        https.setTimeout(30000); // 30秒超时

        int httpCode = https.GET();

        if (httpCode == HTTP_CODE_OK)
        {
            // 创建临时文件
            File temp_file = LittleFS.open(temp_path, "w");
            if (!temp_file)
            {
                ESP_LOGE(TAG, "Failed to create temp file: %s", temp_path.c_str());
                https.end();
                delete client;
                continue;
            }

            // 下载数据
            WiFiClient *stream = https.getStreamPtr();
            uint8_t buffer[512];
            int total_bytes = 0;

            while (https.connected() && stream->available())
            {
                int bytes_read = stream->readBytes(buffer, sizeof(buffer));
                if (bytes_read > 0)
                {
                    temp_file.write(buffer, bytes_read);
                    total_bytes += bytes_read;
                }
                delay(1); // 让出CPU时间
            }

            temp_file.close();
            https.end();
            delete client;

            // 验证文件
            if (total_bytes > 0)
            {
                voice_error_t verify_result = verify_downloaded_file(temp_path, file_info.checksum);
                if (verify_result == VOICE_ERR_OK)
                {
                    // 移动临时文件到最终位置
                    if (LittleFS.exists(local_path))
                    {
                        LittleFS.remove(local_path);
                    }

                    File temp_read = LittleFS.open(temp_path, "r");
                    File final_write = LittleFS.open(local_path, "w");

                    if (temp_read && final_write)
                    {
                        uint8_t copy_buffer[512];
                        while (temp_read.available())
                        {
                            int bytes_read = temp_read.read(copy_buffer, sizeof(copy_buffer));
                            final_write.write(copy_buffer, bytes_read);
                        }
                        temp_read.close();
                        final_write.close();

                        LittleFS.remove(temp_path);

                        ESP_LOGI(TAG, "Successfully downloaded: %s (%d bytes)",
                                 file_info.filename.c_str(), total_bytes);
                        return VOICE_ERR_OK;
                    }
                }
            }

            // 清理失败的临时文件
            LittleFS.remove(temp_path);
        }
        else
        {
            ESP_LOGE(TAG, "HTTP request failed: %d", httpCode);
            https.end();
            delete client;
        }

        if (retry < MAX_RETRIES - 1)
        {
            ESP_LOGW(TAG, "Download failed, retrying in %d ms...", RETRY_DELAY_MS);
            delay(RETRY_DELAY_MS);
        }
    }

    ESP_LOGE(TAG, "Failed to download after %d attempts: %s", MAX_RETRIES, file_info.filename.c_str());
    return VOICE_ERR_NETWORK_TIMEOUT;
}

/**
 * @brief 验证下载的文件
 */
static voice_error_t verify_downloaded_file(const String &filepath, const String &expected_checksum)
{
    if (expected_checksum.isEmpty())
    {
        ESP_LOGW(TAG, "No checksum provided, skipping verification");
        return VOICE_ERR_OK;
    }

    String calculated_checksum = calculate_file_checksum(filepath);
    if (calculated_checksum.isEmpty())
    {
        ESP_LOGE(TAG, "Failed to calculate file checksum");
        return VOICE_ERR_CHECKSUM_MISMATCH;
    }

    if (calculated_checksum.equalsIgnoreCase(expected_checksum))
    {
        ESP_LOGD(TAG, "File checksum verified: %s", filepath.c_str());
        return VOICE_ERR_OK;
    }
    else
    {
        ESP_LOGE(TAG, "Checksum mismatch for %s: expected %s, got %s",
                 filepath.c_str(), expected_checksum.c_str(), calculated_checksum.c_str());
        return VOICE_ERR_CHECKSUM_MISMATCH;
    }
}

/**
 * @brief 计算文件校验和
 */
static String calculate_file_checksum(const String &filepath)
{
    File file = LittleFS.open(filepath, "r");
    if (!file)
    {
        return String();
    }

    uint32_t crc = 0;
    uint8_t buffer[512];

    while (file.available())
    {
        int bytes_read = file.read(buffer, sizeof(buffer));
        if (bytes_read > 0)
        {
            crc = esp_crc32_le(crc, buffer, bytes_read);
        }
    }

    file.close();

    char crc_str[9];
    sprintf(crc_str, "%08X", crc);
    return String(crc_str);
}

/**
 * @brief 获取本地文件版本
 */
static voice_error_t get_local_file_version(const String &filename, uint16_t *version)
{
    if (!version)
    {
        return VOICE_ERR_INVALID_PARAM;
    }

    String version_file = audio_path + "/." + filename + ".version";

    File file = LittleFS.open(version_file, "r");
    if (!file)
    {
        *version = 0;
        return VOICE_ERR_FILE_NOT_FOUND;
    }

    String version_str = file.readString();
    file.close();

    *version = version_str.toInt();
    return VOICE_ERR_OK;
}

/**
 * @brief 保存本地文件版本
 */
static voice_error_t save_local_file_version(const String &filename, uint16_t version)
{
    String version_file = audio_path + "/." + filename + ".version";

    File file = LittleFS.open(version_file, "w");
    if (!file)
    {
        ESP_LOGE(TAG, "Failed to create version file: %s", version_file.c_str());
        return VOICE_ERR_INIT_FAILED;
    }

    file.print(version);
    file.close();

    ESP_LOGD(TAG, "Saved version %d for file %s", version, filename.c_str());
    return VOICE_ERR_OK;
}

/**
 * @brief 清理旧文件
 */
static voice_error_t cleanup_old_files(void)
{
    ESP_LOGI(TAG, "Cleaning up old voice files...");

    File root = LittleFS.open(audio_path);
    if (!root || !root.isDirectory())
    {
        ESP_LOGE(TAG, "Failed to open audio directory");
        return VOICE_ERR_INIT_FAILED;
    }

    std::vector<String> files_to_delete;

    File file = root.openNextFile();
    while (file)
    {
        String filename = file.name();

        // 删除临时文件
        if (filename.startsWith("temp_"))
        {
            files_to_delete.push_back(audio_path + "/" + filename);
        }

        file = root.openNextFile();
    }

    // 执行删除
    for (const String &filepath : files_to_delete)
    {
        if (LittleFS.remove(filepath))
        {
            ESP_LOGD(TAG, "Removed old file: %s", filepath.c_str());
        }
        else
        {
            ESP_LOGW(TAG, "Failed to remove file: %s", filepath.c_str());
        }
    }

    ESP_LOGI(TAG, "Cleanup completed, removed %d files", files_to_delete.size());
    return VOICE_ERR_OK;
}

/**
 * @brief 批量更新所有语音文件
 */
voice_error_t voice_update_all_from_cloud(void)
{
    ESP_LOGI(TAG, "Starting batch update of all voice files...");

    // 清理旧文件
    cleanup_old_files();

    // 获取云端文件列表
    std::vector<cloud_voice_info_t> cloud_files;
    voice_error_t err = check_cloud_version_info(cloud_files);
    if (err != VOICE_ERR_OK)
    {
        ESP_LOGE(TAG, "Failed to get cloud file list");
        return err;
    }

    int updated_count = 0;
    int failed_count = 0;

    // 逐个检查和更新文件
    for (const auto &cloud_file : cloud_files)
    {
        uint16_t local_version = 0;
        get_local_file_version(cloud_file.filename, &local_version);

        if (cloud_file.version > local_version)
        {
            ESP_LOGI(TAG, "Updating %s: v%d -> v%d",
                     cloud_file.filename.c_str(), local_version, cloud_file.version);

            err = download_voice_file_with_retry(cloud_file);
            if (err == VOICE_ERR_OK)
            {
                save_local_file_version(cloud_file.filename, cloud_file.version);
                updated_count++;
            }
            else
            {
                failed_count++;
                ESP_LOGE(TAG, "Failed to update %s", cloud_file.filename.c_str());
            }
        }
        else
        {
            ESP_LOGD(TAG, "File %s is up to date (v%d)",
                     cloud_file.filename.c_str(), local_version);
        }
    }

    ESP_LOGI(TAG, "Batch update completed: %d updated, %d failed", updated_count, failed_count);
    return (failed_count == 0) ? VOICE_ERR_OK : VOICE_ERR_NETWORK_TIMEOUT;
}

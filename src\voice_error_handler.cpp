#include "voice_hybrid_system.h"
#include <esp_log.h>
#include <time.h>
#include <FS.h>
#include <LittleFS.h>

static const char *TAG = "VoiceError";

// 错误统计结构
typedef struct
{
    uint32_t total_errors;
    uint32_t network_errors;
    uint32_t storage_errors;
    uint32_t compression_errors;
    uint32_t playback_errors;
    uint32_t last_error_time;
    voice_error_t last_error_code;
    char last_error_context[64];
} voice_error_stats_t;

// 日志配置
typedef struct
{
    bool log_to_file;
    bool log_to_serial;
    uint32_t max_log_file_size;
    uint32_t max_log_files;
    esp_log_level_t log_level;
} voice_log_config_t;

// 全局变量
static voice_error_stats_t error_stats = {0};
static voice_log_config_t log_config = {
    .log_to_file = true,
    .log_to_serial = true,
    .max_log_file_size = 100 * 1024, // 100KB
    .max_log_files = 5,
    .log_level = ESP_LOG_INFO};

// 内部函数声明
static const char *voice_error_to_string(voice_error_t error);
static void log_error_to_file(voice_error_t error, const char *context, const char *details);
static void rotate_log_files(void);
static void update_error_statistics(voice_error_t error);
static String get_current_timestamp(void);

/**
 * @brief 记录语音系统错误
 */
void voice_log_error(voice_error_t error, const char *context, const char *details)
{
    if (error == VOICE_ERR_OK)
    {
        return; // 不记录成功状态
    }

    // 更新错误统计
    update_error_statistics(error);

    // 构建错误消息
    String error_msg = String("Voice Error: ") + voice_error_to_string(error);
    if (context)
    {
        error_msg += String(" in ") + context;
    }
    if (details)
    {
        error_msg += String(" - ") + details;
    }

    // 输出到串口
    if (log_config.log_to_serial)
    {
        ESP_LOGE(TAG, "%s", error_msg.c_str());
    }

    // 记录到文件
    if (log_config.log_to_file)
    {
        log_error_to_file(error, context, details);
    }

    // 保存最后的错误信息
    error_stats.last_error_code = error;
    error_stats.last_error_time = millis();
    if (context)
    {
        strncpy(error_stats.last_error_context, context, sizeof(error_stats.last_error_context) - 1);
        error_stats.last_error_context[sizeof(error_stats.last_error_context) - 1] = '\0';
    }
}

/**
 * @brief 记录语音系统信息
 */
void voice_log_info(const char *context, const char *message)
{
    if (!message)
        return;

    String info_msg = String("Voice Info");
    if (context)
    {
        info_msg += String(" [") + context + "]";
    }
    info_msg += String(": ") + message;

    // 输出到串口
    if (log_config.log_to_serial)
    {
        ESP_LOGI(TAG, "%s", info_msg.c_str());
    }

// 记录到文件（仅在调试模式下）
#ifdef VOICE_DEBUG_ENABLED
    if (log_config.log_to_file)
    {
        String timestamp = get_current_timestamp();
        String log_entry = timestamp + " [INFO] " + info_msg + "\n";

        File log_file = LittleFS.open("/voice_system.log", "a");
        if (log_file)
        {
            log_file.print(log_entry);
            log_file.close();
        }
    }
#endif
}

/**
 * @brief 记录语音系统警告
 */
void voice_log_warning(const char *context, const char *message)
{
    if (!message)
        return;

    String warn_msg = String("Voice Warning");
    if (context)
    {
        warn_msg += String(" [") + context + "]";
    }
    warn_msg += String(": ") + message;

    // 输出到串口
    if (log_config.log_to_serial)
    {
        ESP_LOGW(TAG, "%s", warn_msg.c_str());
    }

    // 记录到文件
    if (log_config.log_to_file)
    {
        String timestamp = get_current_timestamp();
        String log_entry = timestamp + " [WARN] " + warn_msg + "\n";

        File log_file = LittleFS.open("/voice_system.log", "a");
        if (log_file)
        {
            log_file.print(log_entry);
            log_file.close();

            // 检查文件大小，必要时轮转
            if (log_file.size() > log_config.max_log_file_size)
            {
                rotate_log_files();
            }
        }
    }
}

/**
 * @brief 获取错误统计信息
 */
void voice_get_error_stats(voice_error_stats_t *stats)
{
    if (stats)
    {
        memcpy(stats, &error_stats, sizeof(voice_error_stats_t));
    }
}

/**
 * @brief 重置错误统计
 */
void voice_reset_error_stats(void)
{
    memset(&error_stats, 0, sizeof(voice_error_stats_t));
    ESP_LOGI(TAG, "Error statistics reset");
}

/**
 * @brief 配置日志系统
 */
void voice_configure_logging(bool log_to_file, bool log_to_serial,
                             uint32_t max_file_size, esp_log_level_t level)
{
    log_config.log_to_file = log_to_file;
    log_config.log_to_serial = log_to_serial;
    log_config.max_log_file_size = max_file_size;
    log_config.log_level = level;

    ESP_LOGI(TAG, "Logging configured: file=%s, serial=%s, max_size=%d, level=%d",
             log_to_file ? "yes" : "no", log_to_serial ? "yes" : "no",
             max_file_size, level);
}

/**
 * @brief 错误代码转字符串
 */
static const char *voice_error_to_string(voice_error_t error)
{
    switch (error)
    {
    case VOICE_ERR_OK:
        return "OK";
    case VOICE_ERR_INVALID_PARAM:
        return "Invalid Parameter";
    case VOICE_ERR_FILE_NOT_FOUND:
        return "File Not Found";
    case VOICE_ERR_DECOMPRESSION_FAILED:
        return "Decompression Failed";
    case VOICE_ERR_CHECKSUM_MISMATCH:
        return "Checksum Mismatch";
    case VOICE_ERR_INSUFFICIENT_MEMORY:
        return "Insufficient Memory";
    case VOICE_ERR_NETWORK_TIMEOUT:
        return "Network Timeout";
    case VOICE_ERR_STORAGE_FULL:
        return "Storage Full";
    case VOICE_ERR_PARTITION_NOT_FOUND:
        return "Partition Not Found";
    case VOICE_ERR_INVALID_HEADER:
        return "Invalid Header";
    case VOICE_ERR_INIT_FAILED:
        return "Initialization Failed";
    default:
        return "Unknown Error";
    }
}

/**
 * @brief 记录错误到文件
 */
static void log_error_to_file(voice_error_t error, const char *context, const char *details)
{
    String timestamp = get_current_timestamp();
    String log_entry = timestamp + " [ERROR] " + voice_error_to_string(error);

    if (context)
    {
        log_entry += String(" in ") + context;
    }
    if (details)
    {
        log_entry += String(" - ") + details;
    }
    log_entry += "\n";

    File log_file = LittleFS.open("/voice_errors.log", "a");
    if (log_file)
    {
        log_file.print(log_entry);
        log_file.close();

        // 检查文件大小，必要时轮转
        if (log_file.size() > log_config.max_log_file_size)
        {
            rotate_log_files();
        }
    }
}

/**
 * @brief 轮转日志文件
 */
static void rotate_log_files(void)
{
    ESP_LOGI(TAG, "Rotating log files...");

    // 删除最旧的日志文件
    for (int i = log_config.max_log_files - 1; i > 0; i--)
    {
        String old_name = "/voice_errors.log." + String(i);
        String new_name = "/voice_errors.log." + String(i + 1);

        if (i == log_config.max_log_files - 1)
        {
            LittleFS.remove(old_name); // 删除最旧的
        }
        else
        {
            if (LittleFS.exists(old_name))
            {
                LittleFS.rename(old_name, new_name);
            }
        }
    }

    // 重命名当前日志文件
    if (LittleFS.exists("/voice_errors.log"))
    {
        LittleFS.rename("/voice_errors.log", "/voice_errors.log.1");
    }

    ESP_LOGI(TAG, "Log rotation completed");
}

/**
 * @brief 更新错误统计
 */
static void update_error_statistics(voice_error_t error)
{
    error_stats.total_errors++;

    switch (error)
    {
    case VOICE_ERR_NETWORK_TIMEOUT:
        error_stats.network_errors++;
        break;
    case VOICE_ERR_STORAGE_FULL:
    case VOICE_ERR_PARTITION_NOT_FOUND:
    case VOICE_ERR_FILE_NOT_FOUND:
        error_stats.storage_errors++;
        break;
    case VOICE_ERR_DECOMPRESSION_FAILED:
    case VOICE_ERR_CHECKSUM_MISMATCH:
        error_stats.compression_errors++;
        break;
    case VOICE_ERR_INIT_FAILED:
        error_stats.playback_errors++;
        break;
    default:
        break;
    }
}

/**
 * @brief 获取当前时间戳
 */
static String get_current_timestamp(void)
{
    time_t now;
    struct tm timeinfo;
    char timestamp[32];

    time(&now);
    localtime_r(&now, &timeinfo);

    strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", &timeinfo);
    return String(timestamp);
}

/**
 * @brief 导出错误日志
 */
String voice_export_error_log(void)
{
    String log_content = "";

    File log_file = LittleFS.open("/voice_errors.log", "r");
    if (log_file)
    {
        while (log_file.available())
        {
            log_content += log_file.readString();
        }
        log_file.close();
    }

    return log_content;
}

/**
 * @brief 清理旧日志文件
 */
void voice_cleanup_logs(void)
{
    ESP_LOGI(TAG, "Cleaning up old log files...");

    for (int i = 1; i <= log_config.max_log_files; i++)
    {
        String log_name = "/voice_errors.log." + String(i);
        if (LittleFS.exists(log_name))
        {
            LittleFS.remove(log_name);
        }
    }

    // 清空当前日志文件
    LittleFS.remove("/voice_errors.log");
    LittleFS.remove("/voice_system.log");

    ESP_LOGI(TAG, "Log cleanup completed");
}
